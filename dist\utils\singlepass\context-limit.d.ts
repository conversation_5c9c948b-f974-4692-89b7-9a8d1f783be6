/**
 * Context Limit Management for Single-Pass Mode
 *
 * Provides size calculation and optimization strategies for managing
 * context limits in full-context mode
 */
export interface FileContent {
    path: string;
    content: string;
    size: number;
    type: 'text' | 'binary';
    language?: string;
}
export interface DirectoryStats {
    totalFiles: number;
    totalSize: number;
    textFiles: number;
    binaryFiles: number;
    largestFile: {
        path: string;
        size: number;
    };
    filesByExtension: Record<string, number>;
}
export interface ContextLimitOptions {
    maxTotalSize: number;
    maxFileSize: number;
    excludePatterns: string[];
    includeExtensions: string[];
    priorityDirectories: string[];
    compressionRatio: number;
}
/**
 * Compute size map for files and directories
 */
export declare function computeSizeMap(root: string, files: Array<FileContent>): [Record<string, number>, Record<string, number>];
/**
 * Analyze directory structure and file distribution
 */
export declare function analyzeDirectory(rootPath: string): DirectoryStats;
/**
 * Optimize file selection for context limits
 */
export declare function optimizeForContextLimit(files: Array<FileContent>, options: ContextLimitOptions): {
    selectedFiles: Array<FileContent>;
    excludedFiles: Array<{
        path: string;
        reason: string;
        size: number;
    }>;
    totalSize: number;
    compressionEstimate: number;
};
/**
 * Get default context limit options
 */
export declare function getDefaultContextLimitOptions(): ContextLimitOptions;
//# sourceMappingURL=context-limit.d.ts.map