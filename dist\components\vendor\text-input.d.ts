/**
 * Advanced Text Input Component
 *
 * Provides sophisticated text input with cursor management, keyboard shortcuts,
 * line continuation, text selection, and paste handling
 */
export interface TextInputProps {
    value: string;
    onChange: (value: string) => void;
    onSubmit?: (value: string) => void;
    placeholder?: string;
    multiline?: boolean;
    maxLines?: number;
    showCursor?: boolean;
    cursorChar?: string;
    disabled?: boolean;
    focus?: boolean;
    onFocus?: () => void;
    onBlur?: () => void;
    onKeyPress?: (ch: string, key: any) => void;
}
/**
 * Advanced text input component with rich editing features
 */
export declare function TextInput({ value, onChange, onSubmit, placeholder, multiline, maxLines, showCursor, cursorChar, disabled, focus, onFocus, onBlur, onKeyPress, }: TextInputProps): import("react/jsx-runtime").JSX.Element;
//# sourceMappingURL=text-input.d.ts.map