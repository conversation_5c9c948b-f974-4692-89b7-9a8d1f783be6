/**
 * Single-Pass File Operations
 *
 * Provides file operation schema and utilities for single-pass mode
 * Supports atomic file operations with validation and rollback
 */
import { z } from 'zod';
/**
 * File operation schema for validation
 */
export declare const FileOperationSchema: z.ZodObject<{
    path: z.ZodString;
    updated_full_content: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    delete: z.ZodOptional<z.ZodNullable<z.ZodBoolean>>;
    move_to: z.ZodOptional<z.ZodNullable<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    path: string;
    delete?: boolean | null | undefined;
    updated_full_content?: string | null | undefined;
    move_to?: string | null | undefined;
}, {
    path: string;
    delete?: boolean | null | undefined;
    updated_full_content?: string | null | undefined;
    move_to?: string | null | undefined;
}>;
export type FileOperation = z.infer<typeof FileOperationSchema>;
export interface FileOperationResult {
    success: boolean;
    path: string;
    operation: 'create' | 'update' | 'delete' | 'move';
    error?: string;
    backup?: string;
}
export interface FileOperationContext {
    rootPath: string;
    dryRun?: boolean;
    createBackups?: boolean;
    backupDir?: string;
}
/**
 * Execute a batch of file operations atomically
 */
export declare function executeFileOperations(operations: FileOperation[], context: FileOperationContext): Promise<{
    success: boolean;
    results: FileOperationResult[];
    error?: string;
}>;
//# sourceMappingURL=file-ops.d.ts.map