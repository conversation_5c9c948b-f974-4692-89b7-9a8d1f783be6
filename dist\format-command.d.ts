/**
 * Command Formatting System
 *
 * Provides intelligent command display formatting with shell quoting,
 * wrapper detection, and user-friendly presentation
 */
/**
 * Format command array for user-friendly display
 */
export declare function formatCommandForDisplay(command: Array<string>): string;
/**
 * Format command for logging purposes
 */
export declare function formatCommandForLogging(command: Array<string>): string;
/**
 * Format command with working directory context
 */
export declare function formatCommandWithContext(command: Array<string>, workdir?: string): string;
/**
 * Truncate long commands for display
 */
export declare function truncateCommand(command: string, maxLength?: number): string;
/**
 * Format command execution result for display
 */
export declare function formatCommandResult(command: Array<string>, exitCode: number, duration: number, workdir?: string): string;
/**
 * Extract command name from command array
 */
export declare function getCommandName(command: Array<string>): string;
/**
 * Check if command is potentially dangerous
 */
export declare function isDangerousCommand(command: Array<string>): boolean;
/**
 * Get command category for display purposes
 */
export declare function getCommandCategory(command: Array<string>): string;
//# sourceMappingURL=format-command.d.ts.map