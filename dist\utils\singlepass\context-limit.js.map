{"version": 3, "file": "context-limit.js", "sourceRoot": "", "sources": ["../../../src/utils/singlepass/context-limit.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,IAAI,CAAC;AAC3C,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,MAAM,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AA4BrD;;GAEG;AACH,MAAM,UAAU,cAAc,CAC5B,IAAY,EACZ,KAAyB;IAEzB,MAAM,WAAW,GAA2B,EAAE,CAAC;IAC/C,MAAM,UAAU,GAA2B,EAAE,CAAC;IAE9C,uBAAuB;IACvB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;IACxC,CAAC;IAED,4BAA4B;IAC5B,MAAM,WAAW,GAAG,IAAI,GAAG,EAAU,CAAC;IACtC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAE1C,6BAA6B;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3C,MAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAChD,IAAI,OAAO,EAAE,CAAC;gBACZ,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC;IACH,CAAC;IAED,kCAAkC;IAClC,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;QAC9B,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,KAAK,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;YAC3D,IAAI,QAAQ,CAAC,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,QAAQ,KAAK,GAAG,EAAE,CAAC;gBACvD,SAAS,IAAI,IAAI,CAAC;YACpB,CAAC;QACH,CAAC;QACD,UAAU,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;IAC9B,CAAC;IAED,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;AACnC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAAC,QAAgB;IAC/C,MAAM,KAAK,GAAmB;QAC5B,UAAU,EAAE,CAAC;QACb,SAAS,EAAE,CAAC;QACZ,SAAS,EAAE,CAAC;QACZ,WAAW,EAAE,CAAC;QACd,WAAW,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;QAClC,gBAAgB,EAAE,EAAE;KACrB,CAAC;IAEF,IAAI,CAAC;QACH,aAAa,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,EAAE;YACzC,KAAK,CAAC,UAAU,EAAE,CAAC;YACnB,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC;YAE7B,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;gBACvC,KAAK,CAAC,WAAW,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;YAC9E,CAAC;YAED,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAErE,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACzB,KAAK,CAAC,SAAS,EAAE,CAAC;YACpB,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,WAAW,EAAE,CAAC;YACtB,CAAC;QACH,CAAC,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,QAAQ,CAAC,6BAA6B,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACrG,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,uBAAuB,CACrC,KAAyB,EACzB,OAA4B;IAO5B,MAAM,aAAa,GAAuB,EAAE,CAAC;IAC7C,MAAM,aAAa,GAA0D,EAAE,CAAC;IAChF,IAAI,SAAS,GAAG,CAAC,CAAC;IAElB,yBAAyB;IACzB,MAAM,gBAAgB,GAAG,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAEzD,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE,CAAC;QACpC,wBAAwB;QACxB,IAAI,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;YACpC,aAAa,CAAC,IAAI,CAAC;gBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM,EAAE,mBAAmB,IAAI,CAAC,IAAI,MAAM,OAAO,CAAC,WAAW,GAAG;gBAChE,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC,CAAC;YACH,SAAS;QACX,CAAC;QAED,0DAA0D;QAC1D,MAAM,aAAa,GAAG,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC;QAC5C,IAAI,aAAa,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;YACzC,aAAa,CAAC,IAAI,CAAC;gBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM,EAAE,kCAAkC,aAAa,MAAM,OAAO,CAAC,YAAY,GAAG;gBACpF,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC,CAAC;YACH,SAAS;QACX,CAAC;QAED,yBAAyB;QACzB,IAAI,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC;YAC1D,aAAa,CAAC,IAAI,CAAC;gBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM,EAAE,yBAAyB;gBACjC,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC,CAAC;YACH,SAAS;QACX,CAAC;QAED,0CAA0C;QAC1C,IAAI,OAAO,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzC,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7C,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7C,aAAa,CAAC,IAAI,CAAC;oBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,MAAM,EAAE,+BAA+B;oBACvC,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB,CAAC,CAAC;gBACH,SAAS;YACX,CAAC;QACH,CAAC;QAED,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzB,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC;IACzB,CAAC;IAED,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAE7E,OAAO,CAAC,gCAAgC,EAAE;QACxC,UAAU,EAAE,KAAK,CAAC,MAAM;QACxB,aAAa,EAAE,aAAa,CAAC,MAAM;QACnC,aAAa,EAAE,aAAa,CAAC,MAAM;QACnC,SAAS;QACT,mBAAmB;KACpB,CAAC,CAAC;IAEH,OAAO;QACL,aAAa;QACb,aAAa;QACb,SAAS;QACT,mBAAmB;KACpB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CACtB,KAAyB,EACzB,OAA4B;IAE5B,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACzB,kCAAkC;QAClC,MAAM,SAAS,GAAG,gBAAgB,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,mBAAmB,CAAC,CAAC;QACxE,MAAM,SAAS,GAAG,gBAAgB,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAExE,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5B,OAAO,SAAS,GAAG,SAAS,CAAC;QAC/B,CAAC;QAED,gDAAgD;QAChD,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,QAAgB,EAAE,mBAA6B;IACvE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,mBAAmB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACpD,IAAI,QAAQ,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAChD,OAAO,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB,CAAC,QAAgB,EAAE,eAAyB;IACpE,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE,CAAC;QACtC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,UAAU,CAAC,QAAgB;IAClC,MAAM,cAAc,GAAG;QACrB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM;QACzE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ;QAC3E,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM;QAC5E,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;QACzE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW;KACtE,CAAC;IAEF,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;IAC5C,OAAO,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACtC,CAAC;AAED;;GAEG;AACH,SAAS,aAAa,CACpB,OAAe,EACf,QAA+C,EAC/C,QAAQ,GAAG,EAAE,EACb,YAAY,GAAG,CAAC;IAEhB,IAAI,YAAY,IAAI,QAAQ;QAAE,OAAO;IAErC,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;QAErC,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACtC,MAAM,IAAI,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEhC,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;gBAClB,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC3B,CAAC;iBAAM,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBACxD,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,iCAAiC;IACnC,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,6BAA6B;IAC3C,OAAO;QACL,YAAY,EAAE,IAAI,GAAG,IAAI,EAAE,MAAM;QACjC,WAAW,EAAE,GAAG,GAAG,IAAI,EAAI,QAAQ;QACnC,eAAe,EAAE;YACf,cAAc;YACd,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;YACP,OAAO;YACP,UAAU;YACV,aAAa;YACb,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;SACV;QACD,iBAAiB,EAAE,EAAE,EAAE,qCAAqC;QAC5D,mBAAmB,EAAE;YACnB,KAAK;YACL,KAAK;YACL,KAAK;YACL,YAAY;YACZ,OAAO;YACP,UAAU;SACX;QACD,gBAAgB,EAAE,GAAG,CAAC,yBAAyB;KAChD,CAAC;AACJ,CAAC"}