/**
 * Confirmation Queue Management Hook
 *
 * Manages multiple confirmation requests with promise-based API
 * Provides queue-based system for handling confirmations
 */
import { useState, useCallback, useRef } from 'react';
/**
 * Custom hook for managing confirmation dialogs
 */
export function useConfirmation() {
    const [currentRequest, setCurrentRequest] = useState(null);
    const [queue, setQueue] = useState([]);
    const requestIdRef = useRef(0);
    /**
     * Submit confirmation result
     */
    const submitConfirmation = useCallback((result) => {
        if (currentRequest) {
            currentRequest.resolve(result);
            setCurrentRequest(null);
            // Process next item in queue
            setQueue(prevQueue => {
                const [nextRequest, ...remainingQueue] = prevQueue;
                if (nextRequest) {
                    setCurrentRequest(nextRequest);
                }
                return remainingQueue;
            });
        }
    }, [currentRequest]);
    /**
     * Request confirmation with promise-based API
     */
    const requestConfirmation = useCallback((prompt, explanation) => {
        return new Promise((resolve, reject) => {
            const id = `confirmation-${++requestIdRef.current}`;
            const request = {
                id,
                prompt,
                explanation,
                resolve,
                reject,
            };
            if (currentRequest) {
                // Add to queue if there's already a pending confirmation
                setQueue(prevQueue => [...prevQueue, request]);
            }
            else {
                // Set as current request if no pending confirmation
                setCurrentRequest(request);
            }
        });
    }, [currentRequest]);
    return {
        submitConfirmation,
        requestConfirmation,
        confirmationPrompt: currentRequest?.prompt || null,
        explanation: currentRequest?.explanation,
        isConfirmationPending: currentRequest !== null,
    };
}
//# sourceMappingURL=use-confirmation.js.map