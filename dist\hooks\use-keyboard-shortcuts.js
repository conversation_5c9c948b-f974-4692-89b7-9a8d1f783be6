/**
 * Keyboard Shortcuts Management Hook
 *
 * Provides centralized keyboard shortcut handling for the terminal interface
 * Supports context-aware shortcuts and modifier key combinations
 */
import { useEffect, useCallback, useRef } from 'react';
/**
 * Custom hook for managing keyboard shortcuts
 */
export function useKeyboardShortcuts(shortcuts, context, enabled = true) {
    const shortcutsRef = useRef(shortcuts);
    const enabledRef = useRef(enabled);
    // Update refs when props change
    useEffect(() => {
        shortcutsRef.current = shortcuts;
        enabledRef.current = enabled;
    }, [shortcuts, enabled]);
    /**
     * Handle key press events
     */
    const handleKeyPress = useCallback((ch, key) => {
        if (!enabledRef.current)
            return;
        const activeShortcuts = shortcutsRef.current.filter(shortcut => !shortcut.context || shortcut.context === context);
        for (const shortcut of activeShortcuts) {
            if (matchesShortcut(key, shortcut)) {
                shortcut.action();
                return;
            }
        }
    }, [context]);
    /**
     * Register a new shortcut
     */
    const registerShortcut = useCallback((shortcut) => {
        shortcutsRef.current = [...shortcutsRef.current, shortcut];
    }, []);
    /**
     * Unregister a shortcut by key
     */
    const unregisterShortcut = useCallback((key) => {
        shortcutsRef.current = shortcutsRef.current.filter(s => s.key !== key);
    }, []);
    /**
     * Get all registered shortcuts
     */
    const getShortcuts = useCallback(() => {
        return shortcutsRef.current.filter(shortcut => !shortcut.context || shortcut.context === context);
    }, [context]);
    // Set up keyboard event handling
    useEffect(() => {
        if (typeof process !== 'undefined' && process.stdin) {
            process.stdin.on('keypress', handleKeyPress);
            return () => {
                process.stdin.off('keypress', handleKeyPress);
            };
        }
    }, [handleKeyPress]);
    return {
        registerShortcut,
        unregisterShortcut,
        getShortcuts,
    };
}
/**
 * Check if a key event matches a shortcut definition
 */
function matchesShortcut(key, shortcut) {
    if (key.name !== shortcut.key)
        return false;
    if (shortcut.ctrl && !key.ctrl)
        return false;
    if (shortcut.alt && !key.alt)
        return false;
    if (shortcut.shift && !key.shift)
        return false;
    if (shortcut.meta && !key.meta)
        return false;
    // If shortcut doesn't specify modifier, key shouldn't have it
    if (!shortcut.ctrl && key.ctrl)
        return false;
    if (!shortcut.alt && key.alt)
        return false;
    if (!shortcut.shift && key.shift)
        return false;
    if (!shortcut.meta && key.meta)
        return false;
    return true;
}
/**
 * Default keyboard shortcuts for the application
 */
export const DEFAULT_SHORTCUTS = [
    {
        key: 'escape',
        description: 'Close overlay or exit',
        action: () => { }, // Will be overridden by components
        context: 'global',
    },
    {
        key: 'h',
        ctrl: true,
        description: 'Show help',
        action: () => { }, // Will be overridden by components
        context: 'global',
    },
    {
        key: 'm',
        ctrl: true,
        description: 'Open model selector',
        action: () => { }, // Will be overridden by components
        context: 'global',
    },
    {
        key: 'r',
        ctrl: true,
        description: 'Open history',
        action: () => { }, // Will be overridden by components
        context: 'global',
    },
    {
        key: 's',
        ctrl: true,
        description: 'Open sessions',
        action: () => { }, // Will be overridden by components
        context: 'global',
    },
    {
        key: 'a',
        ctrl: true,
        description: 'Open approval settings',
        action: () => { }, // Will be overridden by components
        context: 'global',
    },
    {
        key: 'l',
        ctrl: true,
        description: 'Clear screen',
        action: () => { }, // Will be overridden by components
        context: 'global',
    },
];
/**
 * Format shortcut for display
 */
export function formatShortcut(shortcut) {
    const parts = [];
    if (shortcut.ctrl)
        parts.push('Ctrl');
    if (shortcut.alt)
        parts.push('Alt');
    if (shortcut.shift)
        parts.push('Shift');
    if (shortcut.meta)
        parts.push('Meta');
    parts.push(shortcut.key.toUpperCase());
    return parts.join('+');
}
//# sourceMappingURL=use-keyboard-shortcuts.js.map