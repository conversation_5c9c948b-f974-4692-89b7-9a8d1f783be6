/**
 * Terminal Management Utilities
 *
 * Provides terminal state management, screen clearing, and renderer integration
 * Handles terminal setup, cleanup, and debugging features
 */
import type { Instance } from 'ink';
/**
 * Set the Ink renderer instance for terminal management
 */
export declare function setInkRenderer(renderer: Instance): void;
/**
 * Get the current Ink renderer instance
 */
export declare function getInkRenderer(): Instance | null;
/**
 * Clear the terminal screen and scrollback buffer
 */
export declare function clearTerminal(): void;
/**
 * Clear only the current screen (preserve scrollback)
 */
export declare function clearScreen(): void;
/**
 * Move cursor to specific position
 */
export declare function moveCursor(row: number, col: number): void;
/**
 * Hide cursor
 */
export declare function hideCursor(): void;
/**
 * Show cursor
 */
export declare function showCursor(): void;
/**
 * Enable alternative screen buffer
 */
export declare function enableAlternateScreen(): void;
/**
 * Disable alternative screen buffer
 */
export declare function disableAlternateScreen(): void;
/**
 * Set terminal title
 */
export declare function setTerminalTitle(title: string): void;
/**
 * Restore terminal title
 */
export declare function restoreTerminalTitle(): void;
/**
 * Enable debug mode for terminal operations
 */
export declare function enableDebugMode(): void;
/**
 * Disable debug mode
 */
export declare function disableDebugMode(): void;
/**
 * Check if debug mode is enabled
 */
export declare function isDebugMode(): boolean;
/**
 * Log debug information if debug mode is enabled
 */
export declare function debugLog(message: string, data?: any): void;
/**
 * Measure and log FPS for UI performance debugging
 */
export declare function measureFPS(): () => void;
/**
 * Handle graceful terminal cleanup on exit
 */
export declare function onExit(): void;
/**
 * Set up terminal for application use
 */
export declare function setupTerminal(): void;
/**
 * Get terminal capabilities
 */
export declare function getTerminalCapabilities(): {
    isTTY: boolean;
    hasColors: boolean;
    columns: number;
    rows: number;
    supportsUnicode: boolean;
    platform: string;
};
//# sourceMappingURL=terminal.d.ts.map