/**
 * Keyboard Shortcuts Management Hook
 *
 * Provides centralized keyboard shortcut handling for the terminal interface
 * Supports context-aware shortcuts and modifier key combinations
 */
export interface KeyboardShortcut {
    key: string;
    ctrl?: boolean;
    alt?: boolean;
    shift?: boolean;
    meta?: boolean;
    description: string;
    action: () => void;
    context?: string;
}
/**
 * Custom hook for managing keyboard shortcuts
 */
export declare function useKeyboardShortcuts(shortcuts: KeyboardShortcut[], context?: string, enabled?: boolean): {
    registerShortcut: (shortcut: KeyboardShortcut) => void;
    unregisterShortcut: (key: string) => void;
    getShortcuts: () => KeyboardShortcut[];
};
/**
 * Default keyboard shortcuts for the application
 */
export declare const DEFAULT_SHORTCUTS: KeyboardShortcut[];
/**
 * Format shortcut for display
 */
export declare function formatShortcut(shortcut: KeyboardShortcut): string;
//# sourceMappingURL=use-keyboard-shortcuts.d.ts.map