/**
 * Terminal Size Management Hook
 *
 * Provides dynamic terminal size tracking with resize event handling
 * Accounts for terminal padding and provides fallback values
 */
interface TerminalSize {
    columns: number;
    rows: number;
    width: number;
    height: number;
}
/**
 * Custom hook for tracking terminal size
 */
export declare function useTerminalSize(): TerminalSize;
/**
 * Get current terminal size synchronously
 */
export declare function getTerminalSize(): TerminalSize;
/**
 * Check if terminal size is adequate for the application
 */
export declare function isTerminalSizeAdequate(minColumns?: number, minRows?: number): boolean;
/**
 * Get responsive layout configuration based on terminal size
 */
export declare function getResponsiveLayout(): {
    isSmall: boolean;
    isMedium: boolean;
    isLarge: boolean;
    layout: 'compact' | 'normal' | 'expanded';
};
export {};
//# sourceMappingURL=use-terminal-size.d.ts.map