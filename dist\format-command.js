/**
 * Command Formatting System
 *
 * Provides intelligent command display formatting with shell quoting,
 * wrapper detection, and user-friendly presentation
 */
/**
 * Format command array for user-friendly display
 */
export function formatCommandForDisplay(command) {
    if (!command || command.length === 0) {
        return '';
    }
    try {
        // Handle bash -lc wrapper unwrapping
        const unwrapped = unwrapBashCommand(command);
        // Apply proper shell quoting
        const quoted = unwrapped.map(arg => shellQuote(arg));
        return quoted.join(' ');
    }
    catch (error) {
        // Fallback to simple join if formatting fails
        return command.join(' ');
    }
}
/**
 * Unwrap bash -lc command wrappers
 */
function unwrapBashCommand(command) {
    // Check for bash -lc wrapper pattern
    if (command.length >= 3 &&
        command[0] === 'bash' &&
        command[1] === '-lc') {
        // Extract the actual command from the bash -lc wrapper
        const wrappedCommand = command[2];
        try {
            // Simple parsing - split on spaces but respect quotes
            return parseShellCommand(wrappedCommand);
        }
        catch {
            // If parsing fails, return the wrapped command as-is
            return [wrappedCommand];
        }
    }
    // Check for other shell wrappers
    if (command.length >= 3 &&
        (command[0] === 'sh' || command[0] === 'zsh') &&
        command[1] === '-c') {
        const wrappedCommand = command[2];
        try {
            return parseShellCommand(wrappedCommand);
        }
        catch {
            return [wrappedCommand];
        }
    }
    return command;
}
/**
 * Parse shell command string into array of arguments
 */
function parseShellCommand(commandString) {
    const args = [];
    let current = '';
    let inQuotes = false;
    let quoteChar = '';
    let escaped = false;
    for (let i = 0; i < commandString.length; i++) {
        const char = commandString[i];
        if (escaped) {
            current += char;
            escaped = false;
            continue;
        }
        if (char === '\\') {
            escaped = true;
            continue;
        }
        if (!inQuotes && (char === '"' || char === "'")) {
            inQuotes = true;
            quoteChar = char;
            continue;
        }
        if (inQuotes && char === quoteChar) {
            inQuotes = false;
            quoteChar = '';
            continue;
        }
        if (!inQuotes && char === ' ') {
            if (current) {
                args.push(current);
                current = '';
            }
            continue;
        }
        current += char;
    }
    if (current) {
        args.push(current);
    }
    return args;
}
/**
 * Apply shell quoting to an argument if needed
 */
function shellQuote(arg) {
    // Don't quote if not needed
    if (isSimpleArg(arg)) {
        return arg;
    }
    // Check if already quoted
    if (isAlreadyQuoted(arg)) {
        return arg;
    }
    // Apply appropriate quoting
    if (arg.includes("'")) {
        // Use double quotes if single quotes are present
        return `"${arg.replace(/"/g, '\\"')}"`;
    }
    else {
        // Use single quotes for simplicity
        return `'${arg}'`;
    }
}
/**
 * Check if argument is simple and doesn't need quoting
 */
function isSimpleArg(arg) {
    // Simple arguments contain only alphanumeric, dash, underscore, dot, slash
    return /^[a-zA-Z0-9._/-]+$/.test(arg);
}
/**
 * Check if argument is already properly quoted
 */
function isAlreadyQuoted(arg) {
    const len = arg.length;
    if (len < 2)
        return false;
    const first = arg[0];
    const last = arg[len - 1];
    // Check for matching quotes
    if ((first === '"' && last === '"') || (first === "'" && last === "'")) {
        return true;
    }
    return false;
}
/**
 * Format command for logging purposes
 */
export function formatCommandForLogging(command) {
    return JSON.stringify(command);
}
/**
 * Format command with working directory context
 */
export function formatCommandWithContext(command, workdir) {
    const formattedCommand = formatCommandForDisplay(command);
    if (workdir && workdir !== process.cwd()) {
        return `cd ${shellQuote(workdir)} && ${formattedCommand}`;
    }
    return formattedCommand;
}
/**
 * Truncate long commands for display
 */
export function truncateCommand(command, maxLength = 100) {
    if (command.length <= maxLength) {
        return command;
    }
    const truncated = command.substring(0, maxLength - 3);
    return truncated + '...';
}
/**
 * Format command execution result for display
 */
export function formatCommandResult(command, exitCode, duration, workdir) {
    const formattedCommand = formatCommandWithContext(command, workdir);
    const status = exitCode === 0 ? 'SUCCESS' : 'FAILED';
    const durationMs = Math.round(duration);
    return `[${status}] ${formattedCommand} (${durationMs}ms)`;
}
/**
 * Extract command name from command array
 */
export function getCommandName(command) {
    if (!command || command.length === 0) {
        return '';
    }
    const unwrapped = unwrapBashCommand(command);
    return unwrapped[0] || '';
}
/**
 * Check if command is potentially dangerous
 */
export function isDangerousCommand(command) {
    const dangerousCommands = [
        'rm', 'rmdir', 'del', 'delete',
        'sudo', 'su',
        'chmod', 'chown',
        'mkfs', 'fdisk',
        'dd', 'shred',
        'kill', 'killall',
        'reboot', 'shutdown', 'halt',
        'format', 'diskpart'
    ];
    const commandName = getCommandName(command).toLowerCase();
    return dangerousCommands.includes(commandName);
}
/**
 * Get command category for display purposes
 */
export function getCommandCategory(command) {
    const commandName = getCommandName(command).toLowerCase();
    const categories = {
        'File Operations': ['ls', 'dir', 'cat', 'type', 'cp', 'copy', 'mv', 'move', 'mkdir', 'touch'],
        'Git Operations': ['git'],
        'Package Management': ['npm', 'yarn', 'pnpm', 'pip', 'cargo', 'go'],
        'Build Tools': ['make', 'cmake', 'ninja', 'msbuild'],
        'Text Processing': ['grep', 'findstr', 'sed', 'awk', 'sort', 'uniq'],
        'System Info': ['ps', 'top', 'htop', 'df', 'du', 'free', 'uname', 'whoami'],
        'Network': ['curl', 'wget', 'ping', 'netstat', 'ss'],
        'Dangerous': ['rm', 'del', 'sudo', 'chmod', 'kill']
    };
    for (const [category, commands] of Object.entries(categories)) {
        if (commands.includes(commandName)) {
            return category;
        }
    }
    return 'Other';
}
//# sourceMappingURL=format-command.js.map