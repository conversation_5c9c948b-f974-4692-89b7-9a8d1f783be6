/**
 * Git Diff Utilities
 *
 * Provides comprehensive git diff generation and analysis
 * Supports various diff formats and git operations
 */
export interface GitDiffOptions {
    staged?: boolean;
    cached?: boolean;
    unified?: number;
    nameOnly?: boolean;
    statOnly?: boolean;
    colorWords?: boolean;
    ignoreWhitespace?: boolean;
    files?: string[];
    since?: string;
    until?: string;
}
export interface GitDiffResult {
    success: boolean;
    diff: string;
    files: string[];
    stats?: {
        insertions: number;
        deletions: number;
        filesChanged: number;
    };
    error?: string;
}
/**
 * Generate git diff for current working directory
 */
export declare function getGitDiff(workingDirectory?: string, options?: GitDiffOptions): Promise<GitDiffResult>;
/**
 * Get git status information
 */
export declare function getGitStatus(workingDirectory?: string): Promise<{
    success: boolean;
    status: string;
    files: Array<{
        path: string;
        status: string;
        staged: boolean;
        modified: boolean;
    }>;
    error?: string;
}>;
/**
 * Get current git branch information
 */
export declare function getGitBranch(workingDirectory?: string): Promise<{
    success: boolean;
    branch: string;
    commit: string;
    upstream?: string;
    error?: string;
}>;
/**
 * Check if directory is a git repository
 */
export declare function isGitRepository(workingDirectory?: string): Promise<boolean>;
//# sourceMappingURL=get-diff.d.ts.map