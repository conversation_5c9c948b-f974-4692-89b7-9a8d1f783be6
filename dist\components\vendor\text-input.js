import { jsx as _jsx } from "react/jsx-runtime";
/**
 * Advanced Text Input Component
 *
 * Provides sophisticated text input with cursor management, keyboard shortcuts,
 * line continuation, text selection, and paste handling
 */
import { useState, useEffect, useCallback, useRef } from 'react';
import { Box, Text } from 'ink';
import { useTerminalSize } from '../../hooks/use-terminal-size.js';
/**
 * Advanced text input component with rich editing features
 */
export function TextInput({ value, onChange, onSubmit, placeholder = '', multiline = false, maxLines = 10, showCursor = true, cursorChar = '│', disabled = false, focus = false, onFocus, onBlur, onKeyPress, }) {
    const [cursorPosition, setCursorPosition] = useState(value.length);
    const [selectionStart, setSelectionStart] = useState(-1);
    const [selectionEnd, setSelectionEnd] = useState(-1);
    const [isFocused, setIsFocused] = useState(focus);
    const { columns } = useTerminalSize();
    const inputRef = useRef();
    // Update cursor position when value changes
    useEffect(() => {
        if (cursorPosition > value.length) {
            setCursorPosition(value.length);
        }
    }, [value, cursorPosition]);
    // Handle focus changes
    useEffect(() => {
        setIsFocused(focus);
    }, [focus]);
    /**
     * Handle key press events
     */
    const handleKeyPress = useCallback((ch, key) => {
        if (disabled)
            return;
        // Call external key handler first
        if (onKeyPress) {
            onKeyPress(ch, key);
        }
        // Handle special keys
        if (key.name === 'return' || key.name === 'enter') {
            if (multiline && !key.ctrl) {
                // Insert newline in multiline mode
                const newValue = value.slice(0, cursorPosition) + '\n' + value.slice(cursorPosition);
                onChange(newValue);
                setCursorPosition(cursorPosition + 1);
            }
            else if (onSubmit) {
                // Submit on Enter (or Ctrl+Enter in multiline)
                onSubmit(value);
            }
            return;
        }
        if (key.name === 'backspace') {
            if (cursorPosition > 0) {
                const newValue = value.slice(0, cursorPosition - 1) + value.slice(cursorPosition);
                onChange(newValue);
                setCursorPosition(cursorPosition - 1);
            }
            return;
        }
        if (key.name === 'delete') {
            if (cursorPosition < value.length) {
                const newValue = value.slice(0, cursorPosition) + value.slice(cursorPosition + 1);
                onChange(newValue);
            }
            return;
        }
        // Arrow key navigation
        if (key.name === 'left') {
            setCursorPosition(Math.max(0, cursorPosition - 1));
            return;
        }
        if (key.name === 'right') {
            setCursorPosition(Math.min(value.length, cursorPosition + 1));
            return;
        }
        if (key.name === 'up' && multiline) {
            const lines = value.split('\n');
            const currentLineIndex = getCurrentLineIndex(value, cursorPosition);
            if (currentLineIndex > 0) {
                const currentLineStart = getLineStart(value, cursorPosition);
                const currentColumn = cursorPosition - currentLineStart;
                const prevLineStart = getLineStart(value, currentLineStart - 1);
                const prevLineLength = lines[currentLineIndex - 1].length;
                const newPosition = prevLineStart + Math.min(currentColumn, prevLineLength);
                setCursorPosition(newPosition);
            }
            return;
        }
        if (key.name === 'down' && multiline) {
            const lines = value.split('\n');
            const currentLineIndex = getCurrentLineIndex(value, cursorPosition);
            if (currentLineIndex < lines.length - 1) {
                const currentLineStart = getLineStart(value, cursorPosition);
                const currentColumn = cursorPosition - currentLineStart;
                const nextLineStart = currentLineStart + lines[currentLineIndex].length + 1;
                const nextLineLength = lines[currentLineIndex + 1].length;
                const newPosition = nextLineStart + Math.min(currentColumn, nextLineLength);
                setCursorPosition(newPosition);
            }
            return;
        }
        // Home/End keys
        if (key.name === 'home') {
            const lineStart = getLineStart(value, cursorPosition);
            setCursorPosition(lineStart);
            return;
        }
        if (key.name === 'end') {
            const lineEnd = getLineEnd(value, cursorPosition);
            setCursorPosition(lineEnd);
            return;
        }
        // Ctrl+A (select all)
        if (key.ctrl && key.name === 'a') {
            setSelectionStart(0);
            setSelectionEnd(value.length);
            return;
        }
        // Ctrl+K (kill line)
        if (key.ctrl && key.name === 'k') {
            const lineEnd = getLineEnd(value, cursorPosition);
            const newValue = value.slice(0, cursorPosition) + value.slice(lineEnd);
            onChange(newValue);
            return;
        }
        // Regular character input
        if (ch && !key.ctrl && !key.meta) {
            const newValue = value.slice(0, cursorPosition) + ch + value.slice(cursorPosition);
            onChange(newValue);
            setCursorPosition(cursorPosition + 1);
        }
    }, [value, cursorPosition, multiline, disabled, onChange, onSubmit, onKeyPress]);
    /**
     * Render the input with cursor and selection
     */
    const renderInput = () => {
        if (!value && placeholder) {
            return _jsx(Text, { dimColor: true, children: placeholder });
        }
        const lines = multiline ? value.split('\n') : [value];
        const displayLines = lines.slice(0, maxLines);
        return (_jsx(Box, { flexDirection: "column", children: displayLines.map((line, lineIndex) => {
                const lineStart = getLineStartByIndex(value, lineIndex);
                const lineEnd = lineStart + line.length;
                // Calculate cursor position within this line
                const showCursorInLine = isFocused && showCursor &&
                    cursorPosition >= lineStart && cursorPosition <= lineEnd;
                let displayLine = line;
                if (showCursorInLine) {
                    const cursorCol = cursorPosition - lineStart;
                    displayLine = line.slice(0, cursorCol) + cursorChar + line.slice(cursorCol);
                }
                return (_jsx(Text, { children: displayLine }, lineIndex));
            }) }));
    };
    // Set up keyboard handling
    useEffect(() => {
        if (isFocused && typeof process !== 'undefined' && process.stdin) {
            process.stdin.on('keypress', handleKeyPress);
            return () => {
                process.stdin.off('keypress', handleKeyPress);
            };
        }
    }, [isFocused, handleKeyPress]);
    return (_jsx(Box, { children: renderInput() }));
}
/**
 * Helper functions for text manipulation
 */
function getCurrentLineIndex(text, position) {
    return text.slice(0, position).split('\n').length - 1;
}
function getLineStart(text, position) {
    const beforeCursor = text.slice(0, position);
    const lastNewline = beforeCursor.lastIndexOf('\n');
    return lastNewline === -1 ? 0 : lastNewline + 1;
}
function getLineEnd(text, position) {
    const afterCursor = text.slice(position);
    const nextNewline = afterCursor.indexOf('\n');
    return nextNewline === -1 ? text.length : position + nextNewline;
}
function getLineStartByIndex(text, lineIndex) {
    const lines = text.split('\n');
    let start = 0;
    for (let i = 0; i < lineIndex; i++) {
        start += lines[i].length + 1; // +1 for newline
    }
    return start;
}
//# sourceMappingURL=text-input.js.map