/**
 * Advanced Text Input Component
 * 
 * Provides sophisticated text input with cursor management, keyboard shortcuts,
 * line continuation, text selection, and paste handling
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Box, Text } from 'ink';
import { useTerminalSize } from '../../hooks/use-terminal-size.js';

export interface TextInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit?: (value: string) => void;
  placeholder?: string;
  multiline?: boolean;
  maxLines?: number;
  showCursor?: boolean;
  cursorChar?: string;
  disabled?: boolean;
  focus?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  onKeyPress?: (ch: string, key: any) => void;
}

/**
 * Advanced text input component with rich editing features
 */
export function TextInput({
  value,
  onChange,
  onSubmit,
  placeholder = '',
  multiline = false,
  maxLines = 10,
  showCursor = true,
  cursorChar = '│',
  disabled = false,
  focus = false,
  onFocus,
  onBlur,
  onKeyPress,
}: TextInputProps) {
  const [cursorPosition, setCursorPosition] = useState(value.length);
  const [selectionStart, setSelectionStart] = useState(-1);
  const [selectionEnd, setSelectionEnd] = useState(-1);
  const [isFocused, setIsFocused] = useState(focus);
  const { columns } = useTerminalSize();
  const inputRef = useRef<any>();

  // Update cursor position when value changes
  useEffect(() => {
    if (cursorPosition > value.length) {
      setCursorPosition(value.length);
    }
  }, [value, cursorPosition]);

  // Handle focus changes
  useEffect(() => {
    setIsFocused(focus);
  }, [focus]);

  /**
   * Handle key press events
   */
  const handleKeyPress = useCallback((ch: string, key: any) => {
    if (disabled) return;

    // Call external key handler first
    if (onKeyPress) {
      onKeyPress(ch, key);
    }

    // Handle special keys
    if (key.name === 'return' || key.name === 'enter') {
      if (multiline && !key.ctrl) {
        // Insert newline in multiline mode
        const newValue = value.slice(0, cursorPosition) + '\n' + value.slice(cursorPosition);
        onChange(newValue);
        setCursorPosition(cursorPosition + 1);
      } else if (onSubmit) {
        // Submit on Enter (or Ctrl+Enter in multiline)
        onSubmit(value);
      }
      return;
    }

    if (key.name === 'backspace') {
      if (cursorPosition > 0) {
        const newValue = value.slice(0, cursorPosition - 1) + value.slice(cursorPosition);
        onChange(newValue);
        setCursorPosition(cursorPosition - 1);
      }
      return;
    }

    if (key.name === 'delete') {
      if (cursorPosition < value.length) {
        const newValue = value.slice(0, cursorPosition) + value.slice(cursorPosition + 1);
        onChange(newValue);
      }
      return;
    }

    // Arrow key navigation
    if (key.name === 'left') {
      setCursorPosition(Math.max(0, cursorPosition - 1));
      return;
    }

    if (key.name === 'right') {
      setCursorPosition(Math.min(value.length, cursorPosition + 1));
      return;
    }

    if (key.name === 'up' && multiline) {
      const lines = value.split('\n');
      const currentLineIndex = getCurrentLineIndex(value, cursorPosition);
      if (currentLineIndex > 0) {
        const currentLineStart = getLineStart(value, cursorPosition);
        const currentColumn = cursorPosition - currentLineStart;
        const prevLineStart = getLineStart(value, currentLineStart - 1);
        const prevLineLength = lines[currentLineIndex - 1].length;
        const newPosition = prevLineStart + Math.min(currentColumn, prevLineLength);
        setCursorPosition(newPosition);
      }
      return;
    }

    if (key.name === 'down' && multiline) {
      const lines = value.split('\n');
      const currentLineIndex = getCurrentLineIndex(value, cursorPosition);
      if (currentLineIndex < lines.length - 1) {
        const currentLineStart = getLineStart(value, cursorPosition);
        const currentColumn = cursorPosition - currentLineStart;
        const nextLineStart = currentLineStart + lines[currentLineIndex].length + 1;
        const nextLineLength = lines[currentLineIndex + 1].length;
        const newPosition = nextLineStart + Math.min(currentColumn, nextLineLength);
        setCursorPosition(newPosition);
      }
      return;
    }

    // Home/End keys
    if (key.name === 'home') {
      const lineStart = getLineStart(value, cursorPosition);
      setCursorPosition(lineStart);
      return;
    }

    if (key.name === 'end') {
      const lineEnd = getLineEnd(value, cursorPosition);
      setCursorPosition(lineEnd);
      return;
    }

    // Ctrl+A (select all)
    if (key.ctrl && key.name === 'a') {
      setSelectionStart(0);
      setSelectionEnd(value.length);
      return;
    }

    // Ctrl+K (kill line)
    if (key.ctrl && key.name === 'k') {
      const lineEnd = getLineEnd(value, cursorPosition);
      const newValue = value.slice(0, cursorPosition) + value.slice(lineEnd);
      onChange(newValue);
      return;
    }

    // Regular character input
    if (ch && !key.ctrl && !key.meta) {
      const newValue = value.slice(0, cursorPosition) + ch + value.slice(cursorPosition);
      onChange(newValue);
      setCursorPosition(cursorPosition + 1);
    }
  }, [value, cursorPosition, multiline, disabled, onChange, onSubmit, onKeyPress]);

  /**
   * Render the input with cursor and selection
   */
  const renderInput = () => {
    if (!value && placeholder) {
      return <Text dimColor>{placeholder}</Text>;
    }

    const lines = multiline ? value.split('\n') : [value];
    const displayLines = lines.slice(0, maxLines);

    return (
      <Box flexDirection="column">
        {displayLines.map((line, lineIndex) => {
          const lineStart = getLineStartByIndex(value, lineIndex);
          const lineEnd = lineStart + line.length;
          
          // Calculate cursor position within this line
          const showCursorInLine = isFocused && showCursor && 
            cursorPosition >= lineStart && cursorPosition <= lineEnd;
          
          let displayLine = line;
          if (showCursorInLine) {
            const cursorCol = cursorPosition - lineStart;
            displayLine = line.slice(0, cursorCol) + cursorChar + line.slice(cursorCol);
          }

          return (
            <Text key={lineIndex}>
              {displayLine}
            </Text>
          );
        })}
      </Box>
    );
  };

  // Set up keyboard handling
  useEffect(() => {
    if (isFocused && typeof process !== 'undefined' && process.stdin) {
      process.stdin.on('keypress', handleKeyPress);
      
      return () => {
        process.stdin.off('keypress', handleKeyPress);
      };
    }
  }, [isFocused, handleKeyPress]);

  return (
    <Box>
      {renderInput()}
    </Box>
  );
}

/**
 * Helper functions for text manipulation
 */

function getCurrentLineIndex(text: string, position: number): number {
  return text.slice(0, position).split('\n').length - 1;
}

function getLineStart(text: string, position: number): number {
  const beforeCursor = text.slice(0, position);
  const lastNewline = beforeCursor.lastIndexOf('\n');
  return lastNewline === -1 ? 0 : lastNewline + 1;
}

function getLineEnd(text: string, position: number): number {
  const afterCursor = text.slice(position);
  const nextNewline = afterCursor.indexOf('\n');
  return nextNewline === -1 ? text.length : position + nextNewline;
}

function getLineStartByIndex(text: string, lineIndex: number): number {
  const lines = text.split('\n');
  let start = 0;
  for (let i = 0; i < lineIndex; i++) {
    start += lines[i].length + 1; // +1 for newline
  }
  return start;
}
