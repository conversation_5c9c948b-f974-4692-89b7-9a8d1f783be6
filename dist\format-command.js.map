{"version": 3, "file": "format-command.js", "sourceRoot": "", "sources": ["../src/format-command.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH;;GAEG;AACH,MAAM,UAAU,uBAAuB,CAAC,OAAsB;IAC5D,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACrC,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,IAAI,CAAC;QACH,qCAAqC;QACrC,MAAM,SAAS,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAE7C,6BAA6B;QAC7B,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;QAErD,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE1B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,8CAA8C;QAC9C,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB,CAAC,OAAsB;IAC/C,qCAAqC;IACrC,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC;QACnB,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QACrB,OAAO,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE,CAAC;QAEzB,uDAAuD;QACvD,MAAM,cAAc,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAElC,IAAI,CAAC;YACH,sDAAsD;YACtD,OAAO,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAC3C,CAAC;QAAC,MAAM,CAAC;YACP,qDAAqD;YACrD,OAAO,CAAC,cAAc,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAED,iCAAiC;IACjC,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC;QACnB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC;QAC7C,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;QAExB,MAAM,cAAc,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,CAAC;YACH,OAAO,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAC3C,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,CAAC,cAAc,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB,CAAC,aAAqB;IAC9C,MAAM,IAAI,GAAkB,EAAE,CAAC;IAC/B,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAI,SAAS,GAAG,EAAE,CAAC;IACnB,IAAI,OAAO,GAAG,KAAK,CAAC;IAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC9C,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;QAE9B,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,IAAI,IAAI,CAAC;YAChB,OAAO,GAAG,KAAK,CAAC;YAChB,SAAS;QACX,CAAC;QAED,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;YAClB,OAAO,GAAG,IAAI,CAAC;YACf,SAAS;QACX,CAAC;QAED,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YAChD,QAAQ,GAAG,IAAI,CAAC;YAChB,SAAS,GAAG,IAAI,CAAC;YACjB,SAAS;QACX,CAAC;QAED,IAAI,QAAQ,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACnC,QAAQ,GAAG,KAAK,CAAC;YACjB,SAAS,GAAG,EAAE,CAAC;YACf,SAAS;QACX,CAAC;QAED,IAAI,CAAC,QAAQ,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;YAC9B,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACnB,OAAO,GAAG,EAAE,CAAC;YACf,CAAC;YACD,SAAS;QACX,CAAC;QAED,OAAO,IAAI,IAAI,CAAC;IAClB,CAAC;IAED,IAAI,OAAO,EAAE,CAAC;QACZ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACrB,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACH,SAAS,UAAU,CAAC,GAAW;IAC7B,4BAA4B;IAC5B,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC;QACrB,OAAO,GAAG,CAAC;IACb,CAAC;IAED,0BAA0B;IAC1B,IAAI,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC;QACzB,OAAO,GAAG,CAAC;IACb,CAAC;IAED,4BAA4B;IAC5B,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QACtB,iDAAiD;QACjD,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC;IACzC,CAAC;SAAM,CAAC;QACN,mCAAmC;QACnC,OAAO,IAAI,GAAG,GAAG,CAAC;IACpB,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,WAAW,CAAC,GAAW;IAC9B,2EAA2E;IAC3E,OAAO,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACxC,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,GAAW;IAClC,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;IACvB,IAAI,GAAG,GAAG,CAAC;QAAE,OAAO,KAAK,CAAC;IAE1B,MAAM,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IACrB,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IAE1B,4BAA4B;IAC5B,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;QACvE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,uBAAuB,CAAC,OAAsB;IAC5D,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AACjC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,wBAAwB,CACtC,OAAsB,EACtB,OAAgB;IAEhB,MAAM,gBAAgB,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAC;IAE1D,IAAI,OAAO,IAAI,OAAO,KAAK,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;QACzC,OAAO,MAAM,UAAU,CAAC,OAAO,CAAC,OAAO,gBAAgB,EAAE,CAAC;IAC5D,CAAC;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe,CAC7B,OAAe,EACf,YAAoB,GAAG;IAEvB,IAAI,OAAO,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;QAChC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC;IACtD,OAAO,SAAS,GAAG,KAAK,CAAC;AAC3B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,mBAAmB,CACjC,OAAsB,EACtB,QAAgB,EAChB,QAAgB,EAChB,OAAgB;IAEhB,MAAM,gBAAgB,GAAG,wBAAwB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,MAAM,MAAM,GAAG,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC;IACrD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAExC,OAAO,IAAI,MAAM,KAAK,gBAAgB,KAAK,UAAU,KAAK,CAAC;AAC7D,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,cAAc,CAAC,OAAsB;IACnD,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACrC,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAM,SAAS,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAC7C,OAAO,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AAC5B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAAC,OAAsB;IACvD,MAAM,iBAAiB,GAAG;QACxB,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ;QAC9B,MAAM,EAAE,IAAI;QACZ,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,OAAO;QACf,IAAI,EAAE,OAAO;QACb,MAAM,EAAE,SAAS;QACjB,QAAQ,EAAE,UAAU,EAAE,MAAM;QAC5B,QAAQ,EAAE,UAAU;KACrB,CAAC;IAEF,MAAM,WAAW,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;IAC1D,OAAO,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AACjD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAAC,OAAsB;IACvD,MAAM,WAAW,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;IAE1D,MAAM,UAAU,GAA6B;QAC3C,iBAAiB,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;QAC7F,gBAAgB,EAAE,CAAC,KAAK,CAAC;QACzB,oBAAoB,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC;QACnE,aAAa,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC;QACpD,iBAAiB,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;QACpE,aAAa,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC;QAC3E,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC;QACpD,WAAW,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;KACpD,CAAC;IAEF,KAAK,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;QAC9D,IAAI,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACnC,OAAO,QAAQ,CAAC;QAClB,CAAC;IACH,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC"}